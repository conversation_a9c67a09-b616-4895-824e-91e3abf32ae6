// API 基础配置
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'

// 支持的音频格式
export const SUPPORTED_AUDIO_FORMATS = ['.mp3', '.wav', '.m4a', '.aac', '.flac']

// 文件大小限制 (100MB)
export const MAX_FILE_SIZE = 100 * 1024 * 1024

// 短信验证码相关
export const SMS_COOLDOWN_TIME = 60 // 60秒冷却时间
export const SMS_CODE_LENGTH = 6

// 手机号正则表达式（中国大陆）
export const PHONE_REGEX = /^1[3-9]\d{9}$/

// JWT Token 存储键名
export const TOKEN_KEY = 'auth_token'

// 路由路径
export const ROUTES = {
  HOME: '/home',
} as const

// 转换状态
export const CONVERSION_STATUS = {
  IDLE: 'idle',
  UPLOADING: 'uploading',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  ERROR: 'error',
} as const

// 微信登录相关
export const WECHAT_CONFIG = {
  APP_ID: import.meta.env.VITE_WECHAT_APP_ID || '',
  REDIRECT_URI: import.meta.env.VITE_WECHAT_REDIRECT_URI || '',
}
